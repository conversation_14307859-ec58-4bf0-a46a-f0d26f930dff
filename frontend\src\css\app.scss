// app global css in SCSS form

body {
  // font-family: "OpenSauceSans", "Roboto", "-apple-system", "Helvetica Neue", Helvetica, Arial, sans-serif
}

.no-underline {
  text-decoration: none !important;
}

button.q-btn--rounded {
  border-radius: 8px;
}

.q-icon > img {
  width: auto; //Fixes squishing issue with Firefox
}

.bold-input input {
  font-weight: 800;
}

input.bold-input {
  font-weight: 800;
}

.text-yes {
  color: $YesPrimary;
}

.text-no {
  color: $NoPrimary;
}

.bg-yes {
  background-color: $YesBackground !important;
}

.bg-no {
  background-color: $NoBackground !important;
}

.text-mini {
  font-size: 8px !important;
}

.text-small {
  font-size: 10px !important;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.cursor-help {
  cursor: help;
}

.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.btn-yes {
  color: $YesPrimary;
  background-color: $YesBackground;
}

.btn-no {
  color: $NoPrimary;
  background-color: $NoBackground;
}


//Fonts

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Light.woff2') format('woff2');
  font-display: swap;
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-LightItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Regular.woff2') format('woff2');
  font-display: swap;
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Italic.woff2') format('woff2');
  font-display: swap;
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Medium.woff2') format('woff2');
  font-display: swap;
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-MediumItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-SemiBold.woff2') format('woff2');
  font-display: swap;
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-SemiBoldItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Bold.woff2') format('woff2');
  font-display: swap;
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-BoldItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-ExtraBold.woff2') format('woff2');
  font-display: swap;
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-ExtraBoldItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Black.woff2') format('woff2');
  font-display: swap;
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-BlackItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 900;
  font-style: italic;
}
