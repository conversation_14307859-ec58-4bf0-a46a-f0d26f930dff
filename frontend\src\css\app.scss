// app global css in SCSS form

// Global body styling using design system
body {
  font-family: var(--font-family-primary);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
}

// ===== UTILITY CLASSES =====

.no-underline {
  text-decoration: none !important;
}

.cursor-help {
  cursor: help;
}

.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

// ===== QUASAR COMPONENT OVERRIDES =====

// Button styling
button.q-btn--rounded {
  border-radius: var(--radius-lg);
}

.q-btn {
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

// Icon fixes
.q-icon > img {
  width: auto; //Fixes squishing issue with Firefox
}

// Input styling
.bold-input input,
input.bold-input {
  font-weight: var(--font-weight-extrabold);
}

.q-field {
  .q-field__control {
    background-color: var(--color-surface-primary);
    border-radius: var(--radius-lg);
  }

  .q-field__native {
    color: var(--color-text-primary);
  }

  .q-field__label {
    color: var(--color-text-secondary);
  }

  &.q-field--focused {
    .q-field__control {
      box-shadow: 0 0 0 2px var(--color-primary-light);
    }
  }
}

// ===== LEGACY CLASSES (Updated to use design system) =====

.text-yes {
  color: var(--color-yes-primary);
}

.text-no {
  color: var(--color-no-primary);
}

.bg-yes {
  background-color: var(--color-yes-background) !important;
}

.bg-no {
  background-color: var(--color-no-background) !important;
}

.text-mini {
  font-size: var(--font-size-xs) !important;
}

.text-small {
  font-size: var(--font-size-sm) !important;
}

.btn-yes {
  color: var(--color-yes-primary);
  background-color: var(--color-yes-background);
  border: 1px solid var(--color-yes-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--color-yes-primary);
    color: var(--color-text-primary);
  }
}

.btn-no {
  color: var(--color-no-primary);
  background-color: var(--color-no-background);
  border: 1px solid var(--color-no-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--color-no-primary);
    color: var(--color-text-primary);
  }
}


//Fonts

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Light.woff2') format('woff2');
  font-display: swap;
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-LightItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Regular.woff2') format('woff2');
  font-display: swap;
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Italic.woff2') format('woff2');
  font-display: swap;
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Medium.woff2') format('woff2');
  font-display: swap;
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-MediumItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-SemiBold.woff2') format('woff2');
  font-display: swap;
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-SemiBoldItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Bold.woff2') format('woff2');
  font-display: swap;
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-BoldItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-ExtraBold.woff2') format('woff2');
  font-display: swap;
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-ExtraBoldItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-Black.woff2') format('woff2');
  font-display: swap;
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'OpenSauceSans';
  src: url('/fonts/OpenSauceSans-BlackItalic.woff2') format('woff2');
  font-display: swap;
  font-weight: 900;
  font-style: italic;
}
