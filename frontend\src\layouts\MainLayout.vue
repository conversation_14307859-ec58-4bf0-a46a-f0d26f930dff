<template>
  <q-layout view="lHh Lpr lFf">
    <!-- Toolbar -->
    <q-header elevated>
      <q-toolbar style="height: 50px">
        <!-- Logo -->
        <img class="logo" src="img/logo.png">

        <!-- Search -->
        <q-space />

        <q-input dense outlined
          v-model="search"
          @update:model-value="onInputSearch"
          @focus="onFocusSearch"
          @keydown.enter="onEnterKey"
          @keydown.up="onArrowUp"
          @keydown.down="onArrowDown"
          @keydown.esc="onEscapeKey"
          class="searchbar" placeholder="Search for events..."
        >
          <template v-slot:append>
            <q-btn icon="search" flat square @click="onClickSearch"></q-btn>
          </template>

          <q-menu
            v-model="showSearchResults"
            no-parent-event no-focus
            anchor="bottom left"
            self="top left"
            max-height="600px"
            class="search-results-menu"
          >
            <!-- Loading search results -->
            <q-list v-if="searchStore.quickSearchLoading">
              <q-item>
                <q-item-section>
                  <div class="row justify-center">
                    <q-spinner-dots size="24px" color="primary" />
                  </div>
                </q-item-section>
              </q-item>
            </q-list>
            <!-- Search results -->
            <q-list v-else-if="searchStore.quickSearchResults.length > 0">
              <q-item
                v-for="(event, index) in searchStore.quickSearchResults"
                :key="event.id"
                clickable
                @click="onClickSearchResult(event)"
                :class="['search-result-item', { 'search-result-selected': selectedIndex === index }]"
              >
                <q-item-section avatar>
                  <div class="search-result-avatar">
                    <img :src="event.image" :alt="event.title" />
                  </div>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ event.title }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
            <!-- No search results -->
            <q-list v-else-if="search.length > 0">
              <q-item>
                <q-item-section>
                  <q-item-label class="text-center">No results found</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-input>

        <q-space />

        <!-- Theme Switcher -->
        <ThemeSwitcher class="q-mr-md" />

        <!-- User Profile -->
        <q-btn v-if="userStore.hasLoaded" round flat size="md" class="user-profile-btn" title="Profile/positions"
          @click="onClickUserProfile"
        >
          <q-avatar class="user-avatar">
            <img v-if="userStore.profileImage" :src="userStore.profileImage" loading="lazy" decoding="async">
            <div v-else class="profile-none-toolbar"></div>
          </q-avatar>
        </q-btn>
      </q-toolbar>

      <!-- Header extension -->
      <component v-if="appStore.headerExtensionComp" :is="appStore.headerExtensionComp" v-bind="appStore.headerExtensionProps"></component>
    </q-header>

    <!-- Main content -->
    <q-page-container>
      <div v-if="isMainReady" class="page-wrapper">
        <!-- IMPORTANT: :key="$route.fullPath" makes each change to url (even if only query params) remount the comp. -->
        <!--            For more efficiency, have components watch critical query params and reload themselves on change. -->
        <router-view v-slot="{ Component }" :key="$route.fullPath">
          <!-- Will only render router component when all async setup tags finish -->
          <!-- Setup tag is async if it contains any top level await -->
          <Suspense timeout="1000000">
            <component :is="Component" />
          </Suspense>
        </router-view>
      </div>
    </q-page-container>

    <!-- User Positions Dialog -->
    <UserPositionsDialog />
  </q-layout>
</template>

<script setup lang="ts">
  import { useUserStore } from 'stores/user-store';
  import { useAppStore } from 'stores/app-store';
  import { useSearchStore } from 'stores/search-store';
  import { useRouter } from 'vue-router';
  import { ref, onMounted, watch } from 'vue';
  import { Chart, registerables } from 'chart.js';
  import { PolyGammaEvent } from '@shared/api-dataclasses-shared';
  import UserPositionsDialog from 'src/components/UserPositionsDialog.vue';
  import ThemeSwitcher from 'src/components/ThemeSwitcher.vue';

  const router = useRouter();
  const userStore = useUserStore();
  const appStore = useAppStore();
  const searchStore = useSearchStore();

  //TODO: Move the quicksearch dropdown to its own component

  //Register all chart.js features (category axis, tooltip, legend, etc.)
  Chart.register(...registerables);

  const search = ref('');
  const isMainReady = ref(false);
  const showSearchResults = ref(false);
  const selectedIndex = ref(-1);
  let searchTimeout: ReturnType<typeof setTimeout> | null = null;

  //Watch searchStore.searchQuery and sync with local search ref
  watch(() => searchStore.searchQuery, (newQuery) => {
    search.value = newQuery;
  });

  function onClickSearch()
  {
    if (!search.value.trim()) return;

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    router.push({ path: '/search', query: { q: search.value.trim() } });
    showSearchResults.value = false;
    selectedIndex.value = -1;
  }

  function onInputSearch()
  {
    selectedIndex.value = -1;

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    //Hide results if search is empty
    if (search.value.trim().length === 0) {
      showSearchResults.value = false;
      return;
    }

    showSearchResults.value = true;

    //Debounce search with delay
    searchTimeout = setTimeout(async () => {
      const searchQuery = search.value.trim()
      //Push search text to searchStore filter
      searchStore.setSearchFilters({ query: searchQuery });
      await searchStore.quickSearchEvents(searchQuery, 'active');
      //Reset selection when new results arrive
      selectedIndex.value = -1;
    }, 500);
  }

  function onFocusSearch()
  {
    if (search.value.trim().length === 0) return;

    showSearchResults.value = true;
  }

  function onClickSearchResult(event: PolyGammaEvent)
  {
    search.value = "";
    showSearchResults.value = false;
    selectedIndex.value = -1;
    router.push({ path: `/events/${event.slug}` });
  }

  function onEnterKey(event: KeyboardEvent)
  {
    event.preventDefault();

    // If there's a selected item, navigate to it
    if (selectedIndex.value >= 0 && selectedIndex.value < searchStore.quickSearchResults.length) {
      const selectedEvent = searchStore.quickSearchResults[selectedIndex.value];
      onClickSearchResult(selectedEvent);
    } else {
      // Otherwise, perform regular search
      onClickSearch();
    }
  }

  function onArrowUp(event: KeyboardEvent)
  {
    event.preventDefault();

    if (searchStore.quickSearchResults.length === 0) return;

    if (selectedIndex.value <= 0) {
      selectedIndex.value = searchStore.quickSearchResults.length - 1;
    } else {
      selectedIndex.value--;
    }
  }

  function onArrowDown(event: KeyboardEvent)
  {
    event.preventDefault();

    if (searchStore.quickSearchResults.length === 0) return;

    if (selectedIndex.value >= searchStore.quickSearchResults.length - 1) {
      selectedIndex.value = 0;
    } else {
      selectedIndex.value++;
    }
  }

  function onEscapeKey(event: KeyboardEvent)
  {
    event.preventDefault();
    selectedIndex.value = -1;
    showSearchResults.value = false;
  }

  function onClickUserProfile()
  {
    appStore.showUserPositionsDialog(userStore.storage.walletAddress);
  }

  userStore.fetchUserBalance();

  onMounted(async () => {
    await userStore.loadStorage();
    userStore.fetchUserProfile();
    isMainReady.value = true;
  });
</script>

<style>
  :root {
    --search-image-width: 38px;
  }

  // ===== LAYOUT STRUCTURE =====

  .q-toolbar {
    max-width: 1250px;
    margin: 0 auto;
    background-color: var(--color-surface-primary);
    border-bottom: 1px solid var(--color-border-secondary);
  }

  .page-wrapper {
    max-width: 1250px;
    margin: 0 auto;
    padding: var(--spacing-lg);
  }

  .toolbar-wrapper {
    max-width: 1300px;
    margin: 0 auto;
  }

  // ===== HEADER STYLING =====

  .q-header {
    background-color: var(--color-surface-primary);
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--color-border-secondary);
  }

  .logo {
    height: 50px;
    filter: brightness(1.2); // Brighten logo for dark theme
  }

  // ===== SEARCH BAR STYLING =====

  .searchbar {
    max-width: 500px;
    width: 100%;

    .q-field__control {
      background-color: var(--color-bg-secondary);
      border: 1px solid var(--color-border-secondary);
      border-radius: var(--radius-lg);
      transition: all var(--transition-fast);

      &:hover {
        border-color: var(--color-border-primary);
      }
    }

    .q-field__native {
      color: var(--color-text-primary);

      &::placeholder {
        color: var(--color-text-muted);
      }
    }

    .q-field--focused .q-field__control {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-light);
    }
  }

  .searchbar > div > div {
    padding-right: 0px !important;
    overflow: hidden;
  }

  .searchbar button {
    height: 100%;
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--color-text-secondary);
    transition: color var(--transition-fast);

    &:hover {
      color: var(--color-primary);
    }
  }

  // ===== SEARCH RESULTS STYLING =====

  .search-results-menu {
    min-width: 500px;
    background-color: var(--color-surface-primary);
    border: 1px solid var(--color-border-secondary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
  }

  .search-result-item {
    color: var(--color-text-primary);
    transition: background-color var(--transition-fast);

    &:hover {
      background-color: var(--color-surface-hover);
    }

    .q-item__label {
      font-size: var(--font-size-sm);
      line-height: var(--line-height-normal);
      color: var(--color-text-primary);
    }
  }

  .search-result-selected {
    background-color: var(--color-primary-light) !important;
    border-left: 3px solid var(--color-primary);
  }

  .search-result-avatar {
    width: var(--search-image-width);
    min-width: var(--search-image-width);
    height: var(--search-image-width);
    border-radius: var(--radius-md);
    overflow: hidden;
    border: 1px solid var(--color-border-secondary);
  }

  .search-result-avatar img {
    object-fit: cover;
    width: var(--search-image-width);
    min-width: var(--search-image-width);
    height: var(--search-image-width);
  }

  // ===== USER PROFILE STYLING =====

  .avatar .q-icon {
    font-size: 2.5em;
    color: var(--color-text-secondary);
  }

  .user-profile-btn {
    border: 1px solid var(--color-border-primary);
    background-color: var(--color-surface-secondary);
    max-height: 40px;
    max-width: 40px;
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);

    &:hover {
      border-color: var(--color-primary);
      background-color: var(--color-surface-hover);
    }
  }

  .user-avatar img {
    object-fit: cover;
    width: 95%;
    min-width: 95%;
    height: 95%;
    border-radius: var(--radius-md);
  }

  .profile-none-toolbar {
    background-color: var(--color-text-disabled);
    border-radius: 50%;
    width: 95%;
    min-width: 95%;
    height: 95%;
  }
</style>
