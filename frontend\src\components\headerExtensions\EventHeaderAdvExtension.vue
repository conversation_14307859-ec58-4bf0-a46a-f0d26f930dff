<template>
  <q-toolbar class="header-ext header-title">
    <q-toolbar-title class="text-center">
      <q-img v-if="props.imageUrl.trim() !== ''" :src="imageUrl" class="header-ext-image" fit="cover" />
      <a v-if="eventSlug" :href="`http://polymarket.com/event/${eventSlug}`" target="_blank" rel="noopener noreferrer" class="header-title-link no-underline">
        <strong>{{ titleText }}</strong>
        <q-icon name="open_in_new" size="16px" />
      </a>
    </q-toolbar-title>
  </q-toolbar>
  <q-toolbar class="header-ext header-stats">
    <q-toolbar-title class="row justify-center">
      <!-- Tweet Count -->
      <div v-if="props.tweetCount !== -1" class="column items-center cursor-pointer" :title="`${formatTimeAgoShort(props.tweetLastChange)} ago`"
        @click.stop="onClickTweetCount"
      >
        <div class="text-caption text-grey-7">Tweet #</div>
        <div><a class="text-bold text-no underline-hover">{{ props.tweetCount }}</a></div>
      </div>

      <!-- PnL -->
      <div class="column items-center" :title="`Total rPnl: ${formatCurrency(props.realizedPnl, 0)}`">
        <div class="text-caption text-grey-7">PnL</div>
        <div class="flex items-center">
          <span :class="{'text-yes': props.lowPnl >= 0, 'text-no': props.lowPnl < 0}">{{ formatCurrency(props.lowPnl, 0) }}</span>
          &nbsp;<span class="text-mini">to</span>&nbsp;
          <span :class="{'text-yes': props.highPnl >= 0, 'text-no': props.highPnl < 0}">{{ formatCurrency(props.highPnl, 0) }}</span>
        </div>
      </div>

      <!-- Balance -->
      <div class="column items-center">
        <div class="text-caption text-grey-7">Balance</div>
        <div class="text-yes">
          {{ formatCurrency(user.balance) }}
        </div>
      </div>

      <!-- Buy/Sell Display toggle -->
      <div class="column items-center" title="Show buy or sell prices for markets">
        <div class="text-caption text-grey-7">
          <span :class="!app.isBuyDisplay ? 'text-weight-bold' : ''">Sell</span>
          <span>/</span>
          <span :class="app.isBuyDisplay ? 'text-weight-bold' : ''">Buy</span>
        </div>
        <q-toggle
          v-model="app.isBuyDisplay"
          size="sm"
          :color="app.isBuyDisplay ? 'green' : 'red'" keep-color
        />
      </div>
    </q-toolbar-title>
  </q-toolbar>
  <TweetAnalyzer v-if="props.tweetCount !== -1" v-model:isShown="isAnalyzerShown" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { formatCurrency, formatTimeAgoShort } from 'src/utils';
import { useUserStore } from 'src/stores/user-store';
import { useAppStore } from 'src/stores/app-store';
import TweetAnalyzer from 'src/components/TweetAnalyzer.vue';

const props = defineProps({
  titleText: { type: String, required: true },
  imageUrl: { type: String, default: '' },
  lowPnl: { type: Number, default: 0 },
  highPnl: { type: Number, default: 0 },
  realizedPnl: { type: Number, default: 0 },
  tweetCount: { type: Number, default: -1 },
  tweetLastChange: { type: Date, default: new Date() },
  eventSlug: { type: String },
});

const user = useUserStore();
const app = useAppStore();

const isAnalyzerShown = ref(false);

function onClickTweetCount() {
  isAnalyzerShown.value = true;
}
</script>

<style scoped lang="scss">
.header-ext-image {
  width: 42px;
  height: 42px;
  margin-right: var(--spacing-sm);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-secondary);
}

.header-ext {
  margin: 0px;
  max-width: none;
  background: transparent;
}

.header-title {
  background: linear-gradient(180deg, var(--color-surface-secondary) 0%, var(--color-surface-hover) 100%);
  border-bottom: 1px solid var(--color-border-primary);
}

.header-title-link {
  color: var(--color-text-primary);
  transition: color var(--transition-fast);

  &:hover {
    color: var(--color-primary);
  }

  .q-icon {
    margin-left: var(--spacing-xs);
    opacity: 0.7;
    transition: opacity var(--transition-fast);
  }

  &:hover .q-icon {
    opacity: 1;
  }
}

.header-stats {
  background: linear-gradient(180deg, var(--color-surface-hover) 0%, var(--color-surface-active) 100%);
  border-bottom: 1px solid var(--color-border-primary);
  color: var(--color-text-secondary);
}

.header-stats > .q-toolbar__title > div {
  padding-left: var(--spacing-lg);
  padding-right: var(--spacing-lg);
}

// Stats styling
.text-caption {
  color: var(--color-text-muted) !important;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.text-bold {
  font-weight: var(--font-weight-bold);
}

// Toggle styling
:deep(.q-toggle) {
  .q-toggle__inner--falsy .q-toggle__thumb:after {
    background-color: var(--color-no-primary);
  }

  .q-toggle__inner--truthy .q-toggle__thumb:after {
    background-color: var(--color-yes-primary);
  }

  .q-toggle__track {
    background-color: var(--color-surface-secondary);
    border: 1px solid var(--color-border-secondary);
  }
}

.underline-hover {
  text-decoration: none;
  transition: text-decoration var(--transition-fast);
}

.underline-hover:hover {
  text-decoration: underline;
}

// Cursor pointer for interactive elements
.cursor-pointer {
  cursor: pointer;
  transition: all var(--transition-fast);
}
</style>
