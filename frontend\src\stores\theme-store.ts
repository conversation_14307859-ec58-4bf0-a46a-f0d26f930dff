import { defineStore } from 'pinia';
import { ref, watch } from 'vue';

export interface ThemeColors {
  // Primary Brand Colors
  colorPrimary: string;
  colorPrimaryHover: string;
  colorPrimaryActive: string;
  colorPrimaryLight: string;
  
  // Background Colors
  colorBgPrimary: string;
  colorBgSecondary: string;
  colorBgTertiary: string;
  colorBgElevated: string;
  colorBgOverlay: string;
  
  // Surface Colors
  colorSurfacePrimary: string;
  colorSurfaceSecondary: string;
  colorSurfaceHover: string;
  colorSurfaceActive: string;
  
  // Text Colors
  colorTextPrimary: string;
  colorTextSecondary: string;
  colorTextMuted: string;
  colorTextDisabled: string;
  colorTextInverse: string;
  
  // Border Colors
  colorBorderPrimary: string;
  colorBorderSecondary: string;
  colorBorderFocus: string;
  colorBorderError: string;
  
  // Market-specific Colors
  colorYesPrimary: string;
  colorYesHover: string;
  colorYesBackground: string;
  colorYesLight: string;
  
  colorNoPrimary: string;
  colorNoHover: string;
  colorNoBackground: string;
  colorNoLight: string;
  
  colorOrderPrimary: string;
  colorOrderHover: string;
  colorOrderBackground: string;
  colorOrderLight: string;
  
  // Status Colors
  colorPositive: string;
  colorNegative: string;
  colorWarning: string;
  colorInfo: string;
}

export interface Theme {
  name: string;
  displayName: string;
  colors: ThemeColors;
}

// Default dark theme (matches the design from the image)
const darkTheme: Theme = {
  name: 'dark',
  displayName: 'Dark Theme',
  colors: {
    // Primary Brand Colors
    colorPrimary: '#4a9eff',
    colorPrimaryHover: '#3d8ae6',
    colorPrimaryActive: '#2e7bd4',
    colorPrimaryLight: 'rgba(74, 158, 255, 0.1)',
    
    // Background Colors
    colorBgPrimary: '#1a1d29',
    colorBgSecondary: '#252a3a',
    colorBgTertiary: '#2f3447',
    colorBgElevated: '#353b4f',
    colorBgOverlay: 'rgba(26, 29, 41, 0.95)',
    
    // Surface Colors
    colorSurfacePrimary: '#252a3a',
    colorSurfaceSecondary: '#2f3447',
    colorSurfaceHover: '#353b4f',
    colorSurfaceActive: '#3d4357',
    
    // Text Colors
    colorTextPrimary: '#ffffff',
    colorTextSecondary: '#b8c1d9',
    colorTextMuted: '#8892a6',
    colorTextDisabled: '#5a6478',
    colorTextInverse: '#1a1d29',
    
    // Border Colors
    colorBorderPrimary: '#3d4357',
    colorBorderSecondary: '#2f3447',
    colorBorderFocus: '#4a9eff',
    colorBorderError: '#e74c3c',
    
    // Market-specific Colors
    colorYesPrimary: '#27ae60',
    colorYesHover: '#229954',
    colorYesBackground: 'rgba(39, 174, 96, 0.1)',
    colorYesLight: 'rgba(39, 174, 96, 0.05)',
    
    colorNoPrimary: '#e74c3c',
    colorNoHover: '#d63031',
    colorNoBackground: 'rgba(231, 76, 60, 0.1)',
    colorNoLight: 'rgba(231, 76, 60, 0.05)',
    
    colorOrderPrimary: '#f39c12',
    colorOrderHover: '#e67e22',
    colorOrderBackground: 'rgba(243, 156, 18, 0.1)',
    colorOrderLight: 'rgba(243, 156, 18, 0.05)',
    
    // Status Colors
    colorPositive: '#27ae60',
    colorNegative: '#e74c3c',
    colorWarning: '#f39c12',
    colorInfo: '#3498db',
  }
};

// Light theme alternative
const lightTheme: Theme = {
  name: 'light',
  displayName: 'Light Theme',
  colors: {
    // Primary Brand Colors
    colorPrimary: '#1976d2',
    colorPrimaryHover: '#1565c0',
    colorPrimaryActive: '#0d47a1',
    colorPrimaryLight: 'rgba(25, 118, 210, 0.1)',
    
    // Background Colors
    colorBgPrimary: '#ffffff',
    colorBgSecondary: '#f8f9fa',
    colorBgTertiary: '#e9ecef',
    colorBgElevated: '#ffffff',
    colorBgOverlay: 'rgba(255, 255, 255, 0.95)',
    
    // Surface Colors
    colorSurfacePrimary: '#ffffff',
    colorSurfaceSecondary: '#f8f9fa',
    colorSurfaceHover: '#e9ecef',
    colorSurfaceActive: '#dee2e6',
    
    // Text Colors
    colorTextPrimary: '#212529',
    colorTextSecondary: '#495057',
    colorTextMuted: '#6c757d',
    colorTextDisabled: '#adb5bd',
    colorTextInverse: '#ffffff',
    
    // Border Colors
    colorBorderPrimary: '#dee2e6',
    colorBorderSecondary: '#e9ecef',
    colorBorderFocus: '#1976d2',
    colorBorderError: '#dc3545',
    
    // Market-specific Colors
    colorYesPrimary: '#28a745',
    colorYesHover: '#218838',
    colorYesBackground: 'rgba(40, 167, 69, 0.1)',
    colorYesLight: 'rgba(40, 167, 69, 0.05)',
    
    colorNoPrimary: '#dc3545',
    colorNoHover: '#c82333',
    colorNoBackground: 'rgba(220, 53, 69, 0.1)',
    colorNoLight: 'rgba(220, 53, 69, 0.05)',
    
    colorOrderPrimary: '#fd7e14',
    colorOrderHover: '#e8650e',
    colorOrderBackground: 'rgba(253, 126, 20, 0.1)',
    colorOrderLight: 'rgba(253, 126, 20, 0.05)',
    
    // Status Colors
    colorPositive: '#28a745',
    colorNegative: '#dc3545',
    colorWarning: '#ffc107',
    colorInfo: '#17a2b8',
  }
};

export const useThemeStore = defineStore('theme', () => {
  const currentTheme = ref<Theme>(darkTheme);
  const availableThemes = ref<Theme[]>([darkTheme, lightTheme]);

  // Apply theme colors to CSS variables
  const applyTheme = (theme: Theme) => {
    const root = document.documentElement;
    
    Object.entries(theme.colors).forEach(([key, value]) => {
      // Convert camelCase to kebab-case for CSS variables
      const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      root.style.setProperty(cssVar, value);
    });
  };

  // Set theme and persist to localStorage
  const setTheme = (themeName: string) => {
    const theme = availableThemes.value.find(t => t.name === themeName);
    if (theme) {
      currentTheme.value = theme;
      applyTheme(theme);
      localStorage.setItem('polymarket-theme', themeName);
    }
  };

  // Initialize theme from localStorage or default to dark
  const initializeTheme = () => {
    const savedTheme = localStorage.getItem('polymarket-theme');
    if (savedTheme) {
      setTheme(savedTheme);
    } else {
      setTheme('dark'); // Default to dark theme
    }
  };

  // Custom theme creation
  const createCustomTheme = (name: string, displayName: string, colors: Partial<ThemeColors>) => {
    const baseTheme = currentTheme.value;
    const customTheme: Theme = {
      name,
      displayName,
      colors: { ...baseTheme.colors, ...colors }
    };
    
    availableThemes.value.push(customTheme);
    return customTheme;
  };

  return {
    currentTheme,
    availableThemes,
    setTheme,
    initializeTheme,
    createCustomTheme,
    applyTheme
  };
});
