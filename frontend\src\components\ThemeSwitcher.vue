<template>
  <q-btn-dropdown
    flat
    dense
    :icon="currentThemeIcon"
    :label="isMobile ? '' : 'Theme'"
    class="theme-switcher"
    dropdown-icon="expand_more"
  >
    <q-list>
      <q-item-label header class="text-weight-medium">
        Choose Theme
      </q-item-label>
      
      <q-item
        v-for="theme in availableThemes"
        :key="theme.name"
        clickable
        v-close-popup
        @click="setTheme(theme.name)"
        :class="{ 'theme-item-selected': currentTheme.name === theme.name }"
        class="theme-item"
      >
        <q-item-section avatar>
          <q-icon :name="getThemeIcon(theme.name)" />
        </q-item-section>
        
        <q-item-section>
          <q-item-label>{{ theme.displayName }}</q-item-label>
        </q-item-section>
        
        <q-item-section side v-if="currentTheme.name === theme.name">
          <q-icon name="check" color="primary" />
        </q-item-section>
      </q-item>
      
      <q-separator class="q-my-sm" />
      
      <q-item clickable v-close-popup @click="showCustomThemeDialog = true" class="theme-item">
        <q-item-section avatar>
          <q-icon name="palette" />
        </q-item-section>
        
        <q-item-section>
          <q-item-label>Custom Theme</q-item-label>
          <q-item-label caption>Create your own theme</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </q-btn-dropdown>

  <!-- Custom Theme Dialog -->
  <q-dialog v-model="showCustomThemeDialog" class="custom-theme-dialog">
    <q-card class="custom-theme-card">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Create Custom Theme</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section>
        <q-input
          v-model="customThemeName"
          label="Theme Name"
          outlined
          dense
          class="q-mb-md"
        />
        
        <div class="text-subtitle2 q-mb-sm">Primary Colors</div>
        <div class="color-grid">
          <div class="color-input-group">
            <label>Primary</label>
            <input type="color" v-model="customColors.colorPrimary" class="color-input" />
          </div>
          <div class="color-input-group">
            <label>Background</label>
            <input type="color" v-model="customColors.colorBgPrimary" class="color-input" />
          </div>
          <div class="color-input-group">
            <label>Surface</label>
            <input type="color" v-model="customColors.colorSurfacePrimary" class="color-input" />
          </div>
          <div class="color-input-group">
            <label>Text</label>
            <input type="color" v-model="customColors.colorTextPrimary" class="color-input" />
          </div>
        </div>
        
        <div class="text-subtitle2 q-mb-sm q-mt-md">Market Colors</div>
        <div class="color-grid">
          <div class="color-input-group">
            <label>Yes</label>
            <input type="color" v-model="customColors.colorYesPrimary" class="color-input" />
          </div>
          <div class="color-input-group">
            <label>No</label>
            <input type="color" v-model="customColors.colorNoPrimary" class="color-input" />
          </div>
          <div class="color-input-group">
            <label>Order</label>
            <input type="color" v-model="customColors.colorOrderPrimary" class="color-input" />
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="Cancel" v-close-popup />
        <q-btn 
          color="primary" 
          label="Create Theme" 
          @click="createCustomTheme"
          :disable="!customThemeName.trim()"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useQuasar } from 'quasar';
import { useThemeStore, type ThemeColors } from 'src/stores/theme-store';

const $q = useQuasar();
const themeStore = useThemeStore();

// Reactive references
const showCustomThemeDialog = ref(false);
const customThemeName = ref('');
const customColors = ref<Partial<ThemeColors>>({
  colorPrimary: '#4a9eff',
  colorBgPrimary: '#1a1d29',
  colorSurfacePrimary: '#252a3a',
  colorTextPrimary: '#ffffff',
  colorYesPrimary: '#27ae60',
  colorNoPrimary: '#e74c3c',
  colorOrderPrimary: '#f39c12',
});

// Computed properties
const currentTheme = computed(() => themeStore.currentTheme);
const availableThemes = computed(() => themeStore.availableThemes);
const isMobile = computed(() => $q.screen.lt.sm);

const currentThemeIcon = computed(() => {
  return getThemeIcon(currentTheme.value.name);
});

// Methods
const getThemeIcon = (themeName: string): string => {
  switch (themeName) {
    case 'dark':
      return 'dark_mode';
    case 'light':
      return 'light_mode';
    default:
      return 'palette';
  }
};

const setTheme = (themeName: string) => {
  themeStore.setTheme(themeName);
  $q.notify({
    message: `Switched to ${themeStore.currentTheme.displayName}`,
    color: 'positive',
    position: 'top',
    timeout: 2000,
  });
};

const createCustomTheme = () => {
  const themeName = customThemeName.value.trim().toLowerCase().replace(/\s+/g, '-');
  const displayName = customThemeName.value.trim();
  
  try {
    const newTheme = themeStore.createCustomTheme(themeName, displayName, customColors.value);
    themeStore.setTheme(newTheme.name);
    
    $q.notify({
      message: `Created and applied custom theme: ${displayName}`,
      color: 'positive',
      position: 'top',
      timeout: 3000,
    });
    
    // Reset form
    customThemeName.value = '';
    showCustomThemeDialog.value = false;
  } catch (error) {
    $q.notify({
      message: 'Failed to create custom theme',
      color: 'negative',
      position: 'top',
      timeout: 2000,
    });
  }
};
</script>

<style scoped lang="scss">
.theme-switcher {
  color: var(--color-text-secondary);
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-text-primary);
  }
}

.theme-item {
  transition: background-color var(--transition-fast);
  
  &:hover {
    background-color: var(--color-surface-hover);
  }
  
  &.theme-item-selected {
    background-color: var(--color-primary-light);
    border-left: 3px solid var(--color-primary);
  }
}

.custom-theme-dialog {
  .custom-theme-card {
    min-width: 400px;
    background-color: var(--color-surface-primary);
    color: var(--color-text-primary);
  }
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.color-input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  
  label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
  }
}

.color-input {
  width: 60px;
  height: 40px;
  border: 2px solid var(--color-border-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: border-color var(--transition-fast);
  
  &:hover {
    border-color: var(--color-border-primary);
  }
  
  &:focus {
    border-color: var(--color-primary);
    outline: none;
  }
}
</style>
