<template>
  <div
    class="row items-center q-pa-sm cursor-pointer main-container no-select"
    @click="onClickExpand" v-bind="$attrs"
  >
    <!-- Market name -->
    <div class="col text-h6">
      <span class="market-name" @click.stop="onClickResolutionInfo">{{ marketData.shortName || "Resolution Info" }}</span>
      <q-menu v-model="showResolutionInfo" anchor="top middle" self="bottom middle" class="menu-resolution">
        <div v-html="resolutionText"></div>
      </q-menu>
    </div>

    <!-- Open orders -->
    <div class="col-auto text-left q-pr-sm" style="width: 165px">
      <div v-for="order in props.orderData!" class="text-order">
        <b>{{ `${order.isBuy ? 'BUY' : 'SELL'} ${props.marketData.lookupAssetName(order.assetId)} ` }}</b>
        <span>{{ `${formatDecimal(order.sharesMatched, 0, false)}/${formatDecimal(order.sharesTotal, 0, false)}` }}</span>
        <span class="float-right">{{ `${formatCents(order.price)}` }}</span>
      </div>
    </div>

    <!-- Position/Pnl info -->
    <div class="col-auto text-left" style="width: 140px">
      <!-- shares -->
      <div v-if="props.marketData.positionA" class="text-outcome-yes">
          <b>{{ `${props.marketData.positionA.outcome} ${formatDecimal(props.marketData.positionA.shares, 2, true)}` }}</b><span>{{ ` @ ${formatCents(props.marketData.positionA.avgPrice, 1)}` }}</span>
      </div>
      <div v-if="props.marketData.positionB" class="text-outcome-no">
          <b>{{ `${props.marketData.positionB.outcome} ${formatDecimal(props.marketData.positionB.shares, 2, true)}` }}</b><span>{{ ` @ ${formatCents(props.marketData.positionB.avgPrice, 1)}` }}</span>
      </div>
      <!-- Realized Pnl -->
      <div v-if="props.pnlData && (props.historyData?.length || 0)" class="text-rpnl" :title="`realized pnl: -${formatCurrency(props.pnlData.totalBuys, 0)}(buys) + ${formatCurrency(props.pnlData.totalSells, 0)}(sells) = ${formatCurrency(props.pnlData.realizedPnl, 0)}`">
        <span class="text-mini">rPnL</span>&nbsp;
        <b>{{ formatCurrency(props.pnlData.realizedPnl, 0) }}</b>
      </div>
      <!-- Estimated Pnl -->
      <div v-if="props.pnlData && props.marketData.hasPosition()" title="Estimated current pnl: high is selling at best bid, low is avg price of market selling 25% (capped) of position.">
        <span class="text-mini">PnL</span>&nbsp;
        <span :class="props.pnlData.lowEstimatePnl >= 0 ? 'text-positive' : 'text-negative'">{{ formatCurrency(props.pnlData.lowEstimatePnl, 0) }}</span>
        &nbsp;<span class="text-mini">to</span>&nbsp;
        <span :class="props.pnlData.highEstimatePnl >= 0 ? 'text-positive' : 'text-negative'">{{ formatCurrency(props.pnlData.highEstimatePnl, 0) }}</span>
      </div>
      <div v-if="props.pnlData" class="text-rpnl flex items-center">
        <!-- On Yes Pnl -->
        <div v-if="props.marketData.positionA" title="Whole event Pnl if this market resolves Yes">
          <span class="text-mini">yPnl&nbsp;</span>
          <span :class="props.pnlData.yesPnl >= 0 ? 'text-positive' : 'text-negative'">{{ formatCurrency(props.pnlData.yesPnl, 0) }}</span>&nbsp;
        </div>
        <!-- On No Pnl -->
        <div v-if="props.marketData.positionB" title="Market's pnl if this market resolves No">
          &nbsp;<span class="text-mini">nPnl&nbsp;</span>
          <span :class="props.pnlData.noPnl >= 0 ? 'text-positive' : 'text-negative'">{{ `${(props.pnlData.noPnl >= 0 ? '+' : '')}${formatCurrency(props.pnlData.noPnl, 0)}` }}</span>
        </div>
      </div>
    </div>

    <!-- Chance % -->
    <div class="col-auto text-h5 flex justify-center" style="width: 100px">
      {{ formatDecimal((props.marketData.bestAskA / (props.marketData.bestAskA + props.marketData.bestAskB)) * 100, 1, true) }}%
    </div>

    <!-- Outcomes + prices -->
    <div class="col-auto" style="width: 190px">
      <q-btn-group unelevated class="btn-group-buysell">
        <q-btn toggle no-caps dense
          :label="`${marketData.outcomeNameA} ${formatDecimal(priceA * 100, 1, true)}¢`"
          @click.stop="onClickOutcomeA"
          class="outcome btn-yes" :class="{ 'btn-selected': isOutcomeASelected }"
        />
        <q-btn toggle no-caps dense
          :label="`${marketData.outcomeNameB} ${formatDecimal(priceB * 100, 1, true)}¢`"
          @click.stop="onClickOutcomeB"
          class="outcome btn-no" :class="{ 'btn-selected': isOutcomeBSelected }"
        />
      </q-btn-group>
      <div class="flex justify-evenly">
        <div class="flex justify-center">
          <div class="stat-mini-label">
            Spread:&nbsp;
          </div>
          <div class="stat-mini">
            {{ formatDecimal(marketData.spread! * 100, 1, true) }}¢
          </div>
        </div>
        <div class="flex justify-center">
          <div class="stat-mini-label">
            Vol:&nbsp;
          </div>
          <div class="stat-mini">
            {{ formatCurrency(marketData.volume!, 0) }}
          </div>
        </div>
      </div>
    </div>
  </div>
  <MarketOrderInterface v-if="props.isExpanded"
    :event-id="props.eventId"
    :market-data="marketData"
    :order-data="orderData"
    :history-data="historyData"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { PolyDataHistory, PolyDataMarket, PolyDataOpenOrder } from '@shared/api-dataclasses-shared';
import { formatCurrency, formatCents, formatDecimal, roundDecimal, linkifyText } from 'src/utils';
import MarketOrderInterface from 'src/components/MarketOrderInterface.vue';
import MarketPnLModel from 'src/models/market-pnl-model';

const props = defineProps<{
  eventId: string;
  marketData: PolyDataMarket;
  orderData?: PolyDataOpenOrder[];
  isExpanded?: boolean;
  isOutcomeASelected?: boolean;
  isOutcomeBSelected?: boolean;
  isBuyDisplay?: boolean;
  historyData?: PolyDataHistory[];
  pnlData?: MarketPnLModel;
}>();

const emits = defineEmits(["click", "clickOutcomeA", "clickOutcomeB"]);

const showResolutionInfo = ref(false);

const resolutionText = computed(() => {
  return linkifyText(props.marketData.resolution || "No resolution text available.");
});

const priceA = computed(() => {
  return props.isBuyDisplay ? props.marketData.bestAskA : props.marketData.bestBidA;
});

const priceB = computed(() => {
  return props.isBuyDisplay ? props.marketData.bestAskB : props.marketData.bestBidB;
});

function onClickExpand() {
  emits("click");
}

function onClickOutcomeA() {
  emits("clickOutcomeA");
}

function onClickOutcomeB() {
  emits("clickOutcomeB");
}

function onClickResolutionInfo() {
  //Popup resolution info tooltip
  showResolutionInfo.value = !showResolutionInfo.value;
}
</script>

<style scoped lang="scss">
.main-container {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-xs);
  background: linear-gradient(135deg, var(--color-surface-primary) 0%, var(--color-surface-secondary) 100%);
  border: 1px solid var(--color-border-secondary);
  border-radius: 0;
  padding: var(--spacing-lg);
  margin: 0;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  color: var(--color-text-primary);

  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--color-border-primary);
    background: linear-gradient(135deg, var(--color-surface-secondary) 0%, var(--color-surface-hover) 100%);
  }

  &:first-child {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }

  &:last-child {
    border-bottom-left-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }

  // Remove top border from subsequent cards to create seamless connection
  &:not(:first-child) {
    border-top: none;
    margin-top: -1px;
  }
}

.market-name {
  text-decoration: underline dotted;
  text-decoration-color: var(--color-text-muted);
  text-decoration-thickness: 1px;
  cursor: help;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);

  &:hover {
    color: var(--color-primary);
  }
}

.info-icon {
  color: var(--color-text-muted);
  cursor: help;
  opacity: 0.7;
  transition: all var(--transition-fast);

  &:hover {
    opacity: 1;
    color: var(--color-primary);
  }
}

.outcome {
  width: 95px;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-base);
  padding: var(--spacing-sm) 0;
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;

  &:hover {
    box-shadow: var(--shadow-sm);
  }
}

.outcome:nth-child(even) {
  background-color: var(--color-no-background);
  color: var(--color-no-primary);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  border: 1px solid var(--color-no-primary);
  border-left: none;
}

.outcome:nth-child(odd) {
  background-color: var(--color-yes-background);
  color: var(--color-yes-primary);
  border-radius: var(--radius-md) 0 0 var(--radius-md);
  border: 1px solid var(--color-yes-primary);
  border-right: none;
}

.stat-mini {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  display: flex;
  align-items: center;
}

.stat-mini-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  display: flex;
  align-items: center;
  font-weight: var(--font-weight-medium);
}

.text-outcome-yes {
  color: var(--color-yes-primary);
  background-color: var(--color-yes-background);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.text-outcome-no {
  color: var(--color-no-primary);
  background-color: var(--color-no-background);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.text-win {
  color: var(--color-positive);
  font-weight: var(--font-weight-medium);
}

.text-lose {
  color: var(--color-negative);
  font-weight: var(--font-weight-medium);
}

.text-rpnl {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

.text-order {
  color: var(--color-order-primary);
  background-color: var(--color-order-background);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

:deep(.btn-selected) {
  &.btn-yes {
    background-color: var(--color-yes-primary);
    color: var(--color-text-primary);
    box-shadow: var(--shadow-md);
  }

  &.btn-no {
    background-color: var(--color-no-primary);
    color: var(--color-text-primary);
    box-shadow: var(--shadow-md);
  }
}
</style>

<style lang="scss">
.menu-resolution {
  border-radius: 6px;
  padding: 8px;
  max-width: 800px;
  white-space: pre-line;
}
</style>
