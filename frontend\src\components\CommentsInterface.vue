<template>
  <q-page-sticky position="bottom-right" :offset="[36, 36]">
    <!-- Floating Chat Toggle -->
    <q-btn fab round icon="chat" color="primary"
      @click="onClickToggleChat"
    />
  </q-page-sticky>
  <!-- Chat Window -->
  <div v-if="isChatOpen" class="chat-window col-lg-4 col-md-6">
      <div class="chat-header">
        <span>Comments</span>
        <q-btn icon="close" size="sm" flat @click="onClickCloseChat" />
      </div>

      <!-- Message Input -->
      <div class="chat-input-container row items-center">
        <q-input outlined dense
          v-model="chatInput"
          @keyup.enter="onClickPostComment"
          @click="onClickMessageInput"
          placeholder="Type a message..."
          class="chat-input col"
          ref="elmCommentInput"
        >
          <template v-slot:prepend>
            <q-btn v-if="replyToComment" flat no-caps dense
              icon="alternate_email" color="primary"
              class="btn-reply"
              :label="replyToComment.profile.name"
              @click="replyToComment = null"
            />
          </template>
          <template v-slot:append>
            <q-btn square unelevated
              icon="send"
              class="btn-send"
              color="primary"
              @click="onClickPostComment"
            />
          </template>
        </q-input>
      </div>

      <!-- Comment Area -->
      <div class="chat-scroll row" ref="scrollArea">
        <q-infinite-scroll
        :offset="200"
        @load="onLoadMoreComments"
        :scroll-target="(scrollArea as any)"
      >
        <Comment v-for="comment of eventStore.comments"
          :key="comment.id"
          :comment="comment"
          :curTime="curTime"
          @click="onClickComment"
        />
      </q-infinite-scroll>
      </div>

      <!-- Poly cookie popup -->
      <q-dialog v-model="showCookieDialog" persistent>
        <q-card style="min-width: 350px">
          <q-card-section class="row items-center">
            <div class="text-h6">Polymarket Session Cookie</div>
            <q-icon size="xs" name="sym_o_info" class="text-primary cursor-help text-grey-5" style="margin-left: 6px" title="Submit polymarket auth/session cookie (all start with polymarket*=). Can find in browser devtools in network or cookies tab." />
          </q-card-section>

          <q-card-section class="q-pt-none">
            <q-input v-model="polyCookie" filled type="textarea" class="cookie-input"/>
          </q-card-section>

          <q-card-actions align="right" class="text-primary">
            <q-btn flat label="Cancel" v-close-popup />
            <q-btn flat label="Save" @click="onClickSaveCookie" />
          </q-card-actions>
        </q-card>
      </q-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useEventStore } from "src/stores/event-store";
import { useUserStore } from "src/stores/user-store";
import { PolyDataComment } from "@shared/api-dataclasses-shared";
import Comment from "src/components/Comment.vue";

const commentFetchSize = 40;
const commentPollInterval = 30000;
const curTimeInterval = 10000;
const eventStore = useEventStore();
const user = useUserStore();

const isChatOpen = ref(false);
const chatInput = ref("");
const scrollArea = ref(null);
const showCookieDialog = ref(false);
const polyCookie = ref("");
const curTime = ref(Date.now());
const replyToComment = ref<PolyDataComment | null>(null);
const elmCommentInput = ref<HTMLInputElement | null>(null);

function onClickToggleChat() {
  isChatOpen.value = !isChatOpen.value;
  if (isChatOpen.value) {
    clearComments();
    enableCommentUpdatePolling(true);
  }
  else {
    enableCommentUpdatePolling(false);
    replyToComment.value = null;
    chatInput.value = "";
  }
}

function onClickCloseChat() {
  onClickToggleChat();
}

function onClickMessageInput() {
  if (!user.storage.polySessionCookie) {
    showCookieDialog.value = true;
  }
}

function onClickSaveCookie() {
  user.setPolySessionCookie(polyCookie.value.trim());
  showCookieDialog.value = false;
}

async function onClickPostComment() {
  if (!user.storage.polySessionCookie) {
    showCookieDialog.value = true;
    return;
  }

  const text = chatInput.value.trim();
  if (!text) return;

  let parentCommentId = undefined;
  let replyAddress = undefined;
  if (replyToComment.value) {
    if (replyToComment.value.parentComment) {
      parentCommentId = replyToComment.value.parentComment.id;
    }
    else {
      parentCommentId = replyToComment.value.id;
    }
    replyAddress = replyToComment.value.profile.baseAddress;
  }
  await eventStore.postComment(text, parentCommentId, replyAddress);
  chatInput.value = "";

  updateComments();
}

function onClickComment(comment: PolyDataComment) {
  replyToComment.value = comment;
  elmCommentInput.value!.focus();
}

let timerPollComments = -1;
let timerCurTime = -1;
function enableCommentUpdatePolling(enable: boolean) {
  if (enable) {
    curTime.value = Date.now();
    if (timerPollComments === -1) {
      timerPollComments = setInterval(async () => {
        updateComments();
      }, commentPollInterval) as unknown as number;
    }

    if (timerCurTime === -1) {
      timerCurTime = setInterval(() => {
        curTime.value = Date.now();
      }, curTimeInterval) as unknown as number;
    }
  }
  else {
    if (timerPollComments !== -1) {
      clearInterval(timerPollComments);
      timerPollComments = -1;
    }
    if (timerCurTime !== -1) {
      clearInterval(timerCurTime);
      timerCurTime = -1;
    }
  }
}

function updateComments() {
  onLoadMoreComments(1, () => {});
}

function clearComments() {
  eventStore.clearComments();
}

/**
 * Called when infinite scroll need more comments.
 * @param index starts at 1 and increments by 1 each call to this function.
 * @param done call this when fetch is complete. Pass in true if no more comments to fetch.
 */
function onLoadMoreComments(index: number, done: (stop: boolean) => void) {
  eventStore.fetchComments((index - 1) * commentFetchSize, commentFetchSize)
  .then((numComments) => {
    done(numComments === 0);
  });
}

onMounted(() => {
});
onUnmounted(() => {
  enableCommentUpdatePolling(false);
});
</script>

<style scoped lang="scss">
.chat-window {
  position: fixed;
  z-index: 100;
  bottom: 110px; /* so it doesn"t overlap with the floating button */
  right: 20px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  height: 65%;
  min-height: 400px;
  max-height: 800px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: $primary;
  color: #fff;
  padding: 8px;
  border-radius: 8px 8px 0 0;
}

.chat-scroll {
  padding: 8px;
  flex: 1;
  overflow-y: auto;
  overflow-x: clip;
  width: 100%;
}

:deep(.q-infinite-scroll) {
  overflow-x: auto;
  width: 100%;
}

:deep(.chat-input-container) {
  padding: 8px;
  border-bottom: 1px solid #ccc;

  .chat-input .q-field__control {
    padding-right: 0px;
    padding-left: 4px;
    overflow: hidden;
  }

  .q-field__prepend {
    padding-right: 2px;
  }

  .q-icon {
    margin-right: 0px;
  }

  .btn-send {
    height: 100%;
    width: 50px;
    padding: 4px;
  }
}

.cookie-input {
  border: 1px solid #868686;
  border-radius: 5px;
}

:deep(.btn-reply) {
  margin-right: 0px;

  .q-btn__content {
    display: flex;
    flex-wrap: nowrap;

    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
    }
  }
}
</style>
