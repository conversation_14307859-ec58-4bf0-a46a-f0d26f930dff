// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

// Updated to use our design system colors
$primary   : #4a9eff;
$secondary : #252a3a;
$accent    : #f39c12;

$dark      : #1a1d29;
$dark-page : #1a1d29;

$positive  : #27ae60;
$negative  : #e74c3c;
$info      : #3498db;
$warning   : #f39c12;

//Custom (non-quasar) variables - Updated to use CSS variables from design system

$YesPrimary     : var(--color-yes-primary);
$YesBackground  : var(--color-yes-background);
$NoPrimary      : var(--color-no-primary);
$NoBackground   : var(--color-no-background);
$OrderPrimary   : var(--color-order-primary);
$OrderBackground: var(--color-order-background);

// Additional Quasar theme customizations for dark mode
$separator-color: var(--color-border-secondary);
$separator-dark-color: var(--color-border-primary);

// Body and text colors
body {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-family: var(--font-family-primary);
}
