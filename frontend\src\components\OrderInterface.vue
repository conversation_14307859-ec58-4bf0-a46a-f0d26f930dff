<template>
  <div class="q-px-md q-py-sm">
    <!-- Yes/No Buy/Sell Toggles + Open orders -->
    <q-btn-group unelevated class="btn-group-buysell">
      <q-btn toggle
        label="Buy Yes" no-caps
        @click="isYesSelected = true; isBuySelected = true; isOrdersSelected = false;"
        class="btn-yes" :class="{ 'btn-selected': isYesSelected && isBuySelected && !isOrdersSelected }"
      />
      <q-btn toggle
        label="Sell Yes" no-caps
        @click="isYesSelected = true; isBuySelected = false; isOrdersSelected = false;"
        class="btn-yes" :class="{ 'btn-selected': isYesSelected && !isBuySelected && !isOrdersSelected }"
      />
      <q-btn toggle
        label="Buy No" no-caps
        @click="isYesSelected = false; isBuySelected = true; isOrdersSelected = false;"
        class="btn-no" :class="{ 'btn-selected': !isYesSelected && isBuySelected && !isOrdersSelected }"
      />
      <q-btn toggle
        label="Sell No" no-caps
        @click="isYesSelected = false; isBuySelected = false; isOrdersSelected = false;"
        class="btn-no" :class="{ 'btn-selected': !isYesSelected && !isBuySelected && !isOrdersSelected }"
      />
      <q-btn toggle
        label="Orders" no-caps
        @click="isOrdersSelected = true;"
        class="btn-orders" :class="{ 'btn-selected': isOrdersSelected }"
      />
    </q-btn-group>

    <!-- Order form -->
    <div v-if="!isOrdersSelected">
      <!-- Price Input -->
      <q-input outlined stack-label
        placeholder="50¢, etc." type="text" suffix="¢"
        debounce="500"
        v-model="priceInput"
        :label="priceLabel"
        :rules="[priceRule]"
        class="q-mt-sm"
        @keydown.enter="onInputEnterKey"
      />

      <!-- Shares Input -->
      <q-input outlined stack-label
        debounce="500"
        v-model="sharesInput"
        label="Shares" placeholder="250.5, etc." type="text"
        :rules="[sharesRule]"
        @keydown.enter="onInputEnterKey"
      >
        <template #append>
          <!-- Max shares button -->
          <q-btn v-if="!isBuySelected
                    && ((isYesSelected && marketData.positionA) || (!isYesSelected && marketData.positionB))"
            unelevated dense rounded no-caps
            label="Max" class="text-grey-8"
            @click="onClickMaxShares()"
          />
        </template>
      </q-input>

      <!-- Cost/Gain Display -->
      <div>
        <label class="text-caption">{{ `Est ${isBuySelected ? 'Cost' : 'Gain'}: ` }}</label>
        <span>{{ formatCurrency(orderCost) }}</span>
        <div v-if="isBuySelected" class="float-right">
          <label v-if="isBuySelected" class="text-caption">{{ `Max Profit: ` }}</label>
          <span v-if="isBuySelected">{{ formatCurrency(orderShares - orderCost) }}</span>
        </div>
      </div>

      <!-- Submit Order -->
      <q-btn
        :label="(isBuySelected ? 'Buy ' : 'Sell ') + (isYesSelected ? 'Yes ' : 'No ') + 'Shares'" no-caps
        class="full-width" color="primary"
        :disabled="!orderSubmitEnabled"
        @click="onSubmitOrder"
      />
    </div>

    <!-- Open orders list -->
    <div v-if="isOrdersSelected">
      <!-- Cancel button -->
      <q-btn v-for="order in props.orderData" no-caps flat
        class="full-width btn-orders q-mt-sm"
        @click="onClickCancelOrder(order)"
      >
        <b>{{ `${order.isBuy ? 'BUY' : 'SELL'} ${props.marketData.lookupAssetName(order.assetId)}` }}</b>&nbsp;
        <span>{{ `${formatDecimal(order.sharesMatched,1)}/${formatDecimal(order.sharesTotal,1)} @ ${formatCents(order.price)}` }}</span>&nbsp;
        <q-icon name="cancel" class="text-no" />
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { QBtn, QBtnGroup, QInput } from 'quasar';
import { formatCurrency, formatCents, formatDecimal } from 'src/utils';
import { PolyDataMarket, PolyDataOpenOrder } from '@shared/api-dataclasses-shared';

const props = withDefaults(defineProps<{
  marketData: PolyDataMarket,
  orderData?: PolyDataOpenOrder[],
  priceReadOnly?: boolean,
  priceLabel?: string,
  cost?: number,
}>(), {
  priceReadOnly: false,
  priceLabel: 'Price'
});

const priceInput = defineModel<string>('price', { default: '' });
const sharesInput = defineModel<string>('shares', { default: '' });
const isYesSelected = defineModel<boolean>('isYesSelected', { default: true });
const isBuySelected = defineModel<boolean>('isBuySelected', { default: true });

const emits = defineEmits<{
  (event: 'placeOrder', orderData: { price: number; shares: number, isBuy: boolean, isYes: boolean }): void;
  (event: 'cancelOrders', orders: PolyDataOpenOrder[]): void;
}>()

//Refs

const isOrdersSelected = ref(false);

//Computed

const orderCost = computed(() => {
  return props.cost || ((Number(priceInput.value) || 0) / 100) * (Number(sharesInput.value) || 0);
});
const orderPrice = computed(() => {
  return (Number(priceInput.value) || 0) / 100;
});
const orderShares = computed(() => {
  return Number(sharesInput.value) || 0;
});
const orderSubmitEnabled = computed(() => {
  return orderCost.value != 0 && orderPrice.value != 0 && priceRule(priceInput.value) === true && sharesRule(sharesInput.value) === true;
});

//Functions

const priceRule = (val: string) => {
  if (!val) return true
  const pattern = /^(\d{1,2})(\.\d?)?$/
  if (!pattern.test(val))
    return 'Format: ##.#';
  const num = Number(val);
  if (num < 0.1 || num > 99.9)
    return 'Must be 0.1 to 99.9';
  return true;
};

const sharesRule = (val: string) => {
  if (!val) return true
  if (/\.\d{3,}/.test(val))
    return "Too many decimals";
  const num = Number(val);
  if (!num || num < 0)
    return 'Must be positive decimal';
  if (num < 1)
    return 'Must be greater than 1';
  return true;
};

async function onSubmitOrder() {
  emits("placeOrder", { price: orderPrice.value, shares: orderShares.value, isBuy: isBuySelected.value, isYes: isYesSelected.value });

  //Clear form
  priceInput.value = '';
  sharesInput.value = '';
}

async function onInputEnterKey() {
  if (orderSubmitEnabled.value) {
    await onSubmitOrder();
  }
}

async function onClickCancelOrder(order: PolyDataOpenOrder) {
  emits('cancelOrders', [order]);
}

async function onClickMaxShares() {
  sharesInput.value = formatDecimal(isYesSelected.value ? props.marketData.positionA!.shares : props.marketData.positionB!.shares, 2, false);
}

onMounted(() => {
});
</script>

<style scoped lang="scss">
.q-btn {
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);

  &:hover {
    box-shadow: var(--shadow-sm);
  }
}

.q-btn-group {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-orders {
  color: var(--color-order-primary);
  background-color: var(--color-order-background);
  border: 1px solid var(--color-order-primary);

  &:hover {
    background-color: var(--color-order-primary);
    color: var(--color-text-primary);
  }

  &.btn-selected {
    background-color: var(--color-order-primary);
    color: var(--color-text-primary);
    box-shadow: var(--shadow-md);
  }
}

.btn-group-buysell {
  display: flex;
  margin-bottom: var(--spacing-lg);

  .q-btn {
    flex-grow: 1;
    border-radius: 0;

    &:first-child {
      border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    }

    &:last-child {
      border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    }
  }
}

// Input field styling
:deep(.q-field) {
  .q-field__control {
    background-color: var(--color-surface-secondary);
    border: 1px solid var(--color-border-secondary);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);

    &:hover {
      border-color: var(--color-border-primary);
    }
  }

  .q-field__native {
    color: var(--color-text-primary);
    font-weight: var(--font-weight-medium);

    &::placeholder {
      color: var(--color-text-muted);
    }
  }

  .q-field__label {
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
  }

  &.q-field--focused {
    .q-field__control {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-light);
    }

    .q-field__label {
      color: var(--color-primary);
    }
  }
}

// Cost/Gain display styling
.text-caption {
  color: var(--color-text-muted);
  font-weight: var(--font-weight-medium);
}

// Submit button styling
:deep(.q-btn[color="primary"]) {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
  border: none;
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-top: var(--spacing-lg);

  &:hover {
    background-color: var(--color-primary-hover);
  }

  &:disabled {
    background-color: var(--color-text-disabled);
    color: var(--color-text-muted);
    transform: none;
    box-shadow: none;
  }
}
</style>
