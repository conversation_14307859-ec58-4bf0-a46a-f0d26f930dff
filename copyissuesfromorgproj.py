#!/usr/bin/env python3
"""
Clone every Issue from one Project (v2) to another, keeping key single-select
fields in sync.

• Source:  https://github.com/orgs/PolyEnhancedOrg/projects/1
• Dest  :  https://github.com/users/JortsEnjoyer0/projects/8
• Repo  :  JortsEnjoyer0/poly-enhanced    (new copies get created here)

Requires: Python 3.8+, `pip install requests`
PAT scopes: repo, project
"""

import argparse, os, sys, time, textwrap, requests

GQL = "https://api.github.com/graphql"
REST = "https://api.github.com"

# --------------------------------------------------------------------------- #
#  Small helpers
# --------------------------------------------------------------------------- #
def gql(token, query, variables=None):
    r = requests.post(
        GQL,
        json={"query": query, "variables": variables or {}},
        headers={"Authorization": f"bearer {token}",
                 "Accept": "application/vnd.github+json"},
        timeout=30,
    )
    r.raise_for_status()
    data = r.json()
    if data.get("errors"):
        raise RuntimeError(data["errors"])
    return data["data"]

def rest(token, method, path, **kw):
    r = requests.request(
        method, REST + path,
        headers={"Authorization": f"token {token}",
                 "Accept": "application/vnd.github+json"},
        timeout=30, **kw)
    r.raise_for_status()
    return r.json() if r.text else None

# --------------------------------------------------------------------------- #
#  Static queries / mutations
# --------------------------------------------------------------------------- #
ID_Q = """
query($owner:String!,$num:Int!,$isOrg:Boolean!){
  target: %(type)s(login:$owner){ projectV2(number:$num){ id } }
}
"""

DEST_FIELDS_Q = """
query($pid:ID!){
  node(id:$pid){
    ... on ProjectV2{
      fields(first:100){
        nodes{
          __typename
          ... on ProjectV2SingleSelectField{
            id
            name
            options{ id name }
          }
        }
      }
    }
  }
}
"""

SRC_ITEMS_Q = textwrap.dedent("""
query($pid:ID!,$after:String){
  node(id:$pid){ ... on ProjectV2{
    items(first:100,after:$after){
      pageInfo{ hasNextPage endCursor }
      nodes{
        id
        content{ __typename ... on Issue{
          id number title body repository{nameWithOwner}
        }}
        fieldValues(first:20){
          nodes{
            ... on ProjectV2ItemFieldSingleSelectValue{
              __typename
              field{ ... on ProjectV2SingleSelectField{ name } }
              name
            }
          }
        }
      }
    }
  }}
}
""")

DELETE_ITEM_MUT = """
mutation($pid:ID!,$item:ID!){
  deleteProjectV2Item(input:{projectId:$pid,itemId:$item}){
    deletedItemId
  }
}
"""

ADD_ITEM_MUT = """
mutation($proj:ID!, $content:ID!){
  addProjectV2ItemById(input:{projectId:$proj, contentId:$content}){ item{id} }
}
"""

UPDATE_FIELD_MUT = """
mutation($proj:ID!,$item:ID!,$field:ID!,$opt:String!){
  updateProjectV2ItemFieldValue(
    input:{
      projectId: $proj
      itemId:    $item
      fieldId:   $field
      value: { singleSelectOptionId: $opt }
    }
  ){
    projectV2Item { id }
  }
}
"""


CLOSE_ISSUE_MUT = """
mutation($id:ID!){ closeIssue(input:{issueId:$id}){ issue{ id } } }
"""

# --------------------------------------------------------------------------- #
#  Core logic
# --------------------------------------------------------------------------- #
def get_project_id(token, owner, number, is_org):
    """
    Return the node-ID of a Project v2 board.
    owner      – account login (user or org)
    number     – project number as shown in the URL
    is_org     – True if owner is an org
    """
    kind = "organization" if is_org else "user"

    query = f"""
    query($owner:String!,$num:Int!){{
      target: {kind}(login:$owner){{
        projectV2(number:$num){{ id }}
      }}
    }}
    """

    data = gql(token, query, {"owner": owner, "num": number})
    proj = data["target"]["projectV2"]
    if proj is None:
        raise RuntimeError(
            f"Project #{number} not found under {owner} ({'org' if is_org else 'user'})."
        )
    return proj["id"]

def wipe_destination(token, pid, close_issues=False):
    after = None
    while True:
        data = gql(token, SRC_ITEMS_Q, {"pid": pid, "after": after})
        chunk = data["node"]["items"]
        for itm in chunk["nodes"]:
            # optionally close underlying issue
            if close_issues and itm["content"] and itm["content"]["__typename"] == "Issue":
                gql(token, CLOSE_ISSUE_MUT, {"id": itm["content"]["id"]})
            gql(token, DELETE_ITEM_MUT, {"pid": pid, "item": itm["id"]})
        if not chunk["pageInfo"]["hasNextPage"]:
            break
        after = chunk["pageInfo"]["endCursor"]

def build_field_maps(token, pid):
    nodes = gql(token, DEST_FIELDS_Q, {"pid": pid})["node"]["fields"]["nodes"]
    ids, opts = {}, {}

    for f in nodes:
        if f.get("__typename") != "ProjectV2SingleSelectField":
            continue
        if "id" not in f:              # <- new guard
            continue

        ids[f["name"]]  = f["id"]
        opts[f["name"]] = {o["name"]: o["id"] for o in f["options"]}

    return ids, opts


def iter_source_items(token, pid):
    after=None
    while True:
        data = gql(token, SRC_ITEMS_Q, {"pid": pid, "after": after})
        chunk = data["node"]["items"]
        yield from chunk["nodes"]
        if not chunk["pageInfo"]["hasNextPage"]:
            break
        after = chunk["pageInfo"]["endCursor"]

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--token", default=os.getenv("GH_TOKEN") or os.getenv("GITHUB_TOKEN"),
                    help="GitHub PAT (env GH_TOKEN also works)")
    ap.add_argument("--src-owner", default="PolyEnhancedOrg")
    ap.add_argument("--src-num",   type=int, default=1)
    ap.add_argument("--dst-owner", default="JortsEnjoyer0")
    ap.add_argument("--dst-num",   type=int, default=8)
    ap.add_argument("--dst-repo",  default="JortsEnjoyer0/poly-enhanced")
    ap.add_argument("--really-close", action="store_true",
                    help="Also close the old issues when wiping dest project")
    ap.add_argument("--delay", type=float, default=0.2, help="sleep between writes (rate-limit safety)")
    args = ap.parse_args()

    if not args.token:
        sys.exit("No token supplied – use --token or set GH_TOKEN")

    print("Resolving project IDs …")
    src_pid = get_project_id(args.token, args.src_owner, args.src_num, is_org=True)
    dst_pid = get_project_id(args.token, args.dst_owner, args.dst_num, is_org=False)
    print(f"  Source  : {src_pid}")
    print(f"  Dest    : {dst_pid}")

    print("\nWiping destination project …")
    wipe_destination(args.token, dst_pid, close_issues=args.really_close)

    fld_ids, fld_opts = build_field_maps(args.token, dst_pid)

    print("\nCopying items …")
    for itm in iter_source_items(args.token, src_pid):
        if not itm["content"] or itm["content"]["__typename"] != "Issue":
            continue  # skip Drafts / PRs / etc.
        issue = itm["content"]
        title, body = issue["title"], issue["body"] or ""
        print(f"  → #{issue['number']}  {title[:60]}")

        # 1) create new issue in private repo
        rest_issue = rest(args.token, "POST",
                          f"/repos/{args.dst_repo}/issues",
                          json={"title": title, "body": body})
        new_issue_id = rest_issue["node_id"]

        # 2) add it to dest project
        added = gql(args.token, ADD_ITEM_MUT,
                    {"proj": dst_pid, "content": new_issue_id})["addProjectV2ItemById"]["item"]["id"]

        # 3) copy single-select field values
        for fv in itm["fieldValues"]["nodes"]:
            # only handle single–select values
            if fv.get("__typename") != "ProjectV2ItemFieldSingleSelectValue":
                continue
            if "field" not in fv:          # safety, just in case
                continue

            fname = fv["field"]["name"]
            opt   = fv["name"]
            if fname not in fld_ids or fname not in fld_opts:
                continue
            if opt not in fld_opts[fname]:
                print(f"     ‼ option “{opt}” not found in dest field “{fname}” – skipped")
                continue
            gql(args.token, UPDATE_FIELD_MUT,
                {"proj": dst_pid, "item": added,
                 "field": fld_ids[fname],
                 "opt": fld_opts[fname][opt]})
        time.sleep(args.delay)

    print("\nDone!")

if __name__ == "__main__":
    main()
