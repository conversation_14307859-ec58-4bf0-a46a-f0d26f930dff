<template>
  <q-page class="q-pa-md">
    <div class="test-grid-container">
      <div
        v-for="group in data.groups"
        :key="group.title"
        class="group-subgrid"
      >
        <!-- Header row with avatar and title -->
        <div class="avatar-cell">
          <q-avatar size="40px">
            <img src="/img/default_event.webp" alt="Event Avatar" />
          </q-avatar>
        </div>
        <div class="title-cell">
          {{ group.title }}
        </div>

        <!-- Data rows -->
        <template v-for="(item, index) in group.subItems" :key="index">
          <div class="empty-cell"></div>
          <div class="data-cell">{{ item.cellA }}</div>
          <div class="data-cell">{{ item.cellB }}</div>
          <div class="data-cell">{{ item.cellC }}</div>
        </template>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from "vue";

interface SubItem {
  cellA: string;
  cellB: string;
  cellC: string;
}

interface Group {
  title: string;
  subItems: SubItem[];
}

interface TestData {
  groups: Group[];
}

//Generate random string helper
const generateRandomString = (min: number, max: number): string => {
  const length = Math.floor(Math.random() * (max - min + 1)) + min;
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

//Generate fake test data
const data = ref<TestData>({
  groups: [
    {
      title: "Presidential Election 2024 Markets",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    },
    {
      title: "Cryptocurrency Price Predictions",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    },
    {
      title: "Sports Betting Markets",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    }
  ]
});
</script>

<style scoped>
.test-grid-container {
  display: grid;
  grid-template-columns: 60px max-content max-content max-content;
  gap: var(--spacing-xl) var(--spacing-sm);
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.group-subgrid {
  display: grid;
  grid-template-columns: subgrid;
  grid-column: 1 / -1;
  gap: var(--spacing-sm);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  background: var(--color-surface-primary);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);

  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--color-border-primary);
    transform: translateY(-2px);
  }
}

.avatar-cell {
  grid-row: 1 / -1;
  grid-column: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: var(--spacing-xs);

  .q-avatar {
    border: 2px solid var(--color-border-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: border-color var(--transition-fast);

    &:hover {
      border-color: var(--color-primary);
    }
  }
}

.title-cell {
  grid-column: 2 / -1;
  grid-row: 1;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--color-border-secondary);
  color: var(--color-text-primary);
  height: fit-content;
  margin-bottom: var(--spacing-sm);
}

.empty-cell {
  grid-column: 1;
  /* Spacer cell - no styling needed */
}

.data-cell {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-surface-secondary);
  border-radius: var(--radius-md);
  min-height: 32px;
  display: flex;
  align-items: center;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--color-border-secondary);
  transition: all var(--transition-fast);

  &:hover {
    background: var(--color-surface-hover);
    border-color: var(--color-border-primary);
    color: var(--color-text-primary);
    transform: translateY(-1px);
  }
}
</style>
